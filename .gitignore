# See https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files for more about ignoring files.

# compiled output
dist
tmp
out-tsc

# dependencies
node_modules

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# misc
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings

# System Files
.DS_Store
Thumbs.db

.nx/cache
.nx/workspace-data
.cursor/rules/nx-rules.mdc
.github/instructions/nx.instructions.md

# Next.js
.next
out

# Environment files - NEVER commit these!
.env
.env.local
.env.development
.env.test
.env.production
.env.staging
apps/backend/.env
apps/frontend/.env
apps/frontend/.env.local
apps/frontend/.env.development
apps/frontend/.env.production

# Upload directories
uploads/
apps/backend/uploads/

# Additional logs
*.log
logs/

# Database files
*.db
*.sqlite
*.sqlite3

# Temporary files
*.tmp
*.temp
.cache/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
".env" 
"apps/backend/.env" 
