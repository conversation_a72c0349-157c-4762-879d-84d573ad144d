import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ApplicationsController } from './applications.controller';
import { ApplicationsService } from './applications.service';
import { Application, ApplicationSchema } from '../schemas/application.schema';
import { PatientRequest, PatientRequestSchema } from '../schemas/patient-request.schema';
import { User, UserSchema } from '../schemas/user.schema';
import { Notification, NotificationSchema } from '../schemas/notification.schema';
import { NotificationsService } from '../notifications/notifications.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Application.name, schema: ApplicationSchema },
      { name: PatientRequest.name, schema: PatientRequestSchema },
      { name: User.name, schema: UserSchema },
      { name: Notification.name, schema: NotificationSchema }
    ])
  ],
  controllers: [ApplicationsController],
  providers: [ApplicationsService, NotificationsService],
  exports: [ApplicationsService]
})
export class ApplicationsModule {}
